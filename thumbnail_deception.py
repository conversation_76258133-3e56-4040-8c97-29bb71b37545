#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案三：缩略图欺骗技术
- 缩略图显示B图（平台检测用）
- 正常播放显示A图（用户观看）
"""

import os
import sys
import subprocess
import shutil
import time

def check_ffmpeg():
    """检查FFmpeg工具是否可用"""
    ffmpeg_cmd = None
    ffprobe_cmd = None
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffmpeg_cmd = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffprobe_cmd = cmd
                break
        except:
            continue
    
    return ffmpeg_cmd, ffprobe_cmd

def get_video_info(video_path):
    """获取视频信息"""
    ffmpeg_cmd, ffprobe_cmd = check_ffmpeg()
    if not ffprobe_cmd:
        print("错误: 找不到ffprobe命令")
        return None

    try:
        cmd = [ffprobe_cmd, '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height,r_frame_rate',
               '-show_entries', 'format=duration',
               '-of', 'csv=p=0', video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            print(f"ffprobe命令执行失败: {result.stderr}")
            return None

        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            return None

        video_fields = [f.strip().rstrip(',') for f in lines[0].split(',') if f.strip()]
        format_fields = [f.strip().rstrip(',') for f in lines[1].split(',') if f.strip()]

        if len(video_fields) < 3 or len(format_fields) < 1:
            return None

        width = int(video_fields[0])
        height = int(video_fields[1])
        
        fps_str = video_fields[2]
        if '/' in fps_str:
            num, den = fps_str.split('/')
            fps = float(num) / float(den) if float(den) != 0 else 0
        else:
            fps = float(fps_str) if fps_str else 0

        duration = float(format_fields[0])
        total_frames = int(fps * duration) if fps > 0 else 0
        
        return {
            'width': width,
            'height': height,
            'fps': fps,
            'duration': duration,
            'total_frames': total_frames
        }
        
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None

def extract_frames(video_path, output_dir, target_width, target_height):
    """提取视频帧并统一分辨率"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        print("错误: 找不到ffmpeg命令")
        return False
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [ffmpeg_cmd, '-i', video_path,
               '-vf', f'scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2',
               f'{output_dir}/frame_%06d.png']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            return True
        else:
            print(f"帧提取失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"帧提取异常: {e}")
        return False

def create_thumbnail_deception_sequence(frames_a_dir, frames_b_dir, output_dir, total_frames_a, total_frames_b):
    """创建缩略图欺骗序列
    策略：
    1. 关键帧位置(1, 30, 60, 90...)放置B图 - 缩略图算法会采样这些帧
    2. 其他位置放置A图 - 正常播放主要看到A图
    """
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        # 缩略图采样点：通常在0秒、1秒、2秒...等整数秒位置
        # 假设30fps，则采样帧为：1, 30, 60, 90, 120...
        thumbnail_sample_interval = 30  # 每30帧一个采样点
        
        print(f"创建缩略图欺骗序列:")
        print(f"  - 缩略图采样帧(每{thumbnail_sample_interval}帧): 使用B图")
        print(f"  - 其他帧: 使用A图")
        
        b_frame_used = 0
        a_frame_used = 0
        
        for i in range(total_frames_a):
            target_file = os.path.join(output_dir, f'frame_{i+1:06d}.png')
            frame_number = i + 1
            
            # 判断是否为缩略图采样点
            if frame_number % thumbnail_sample_interval == 1:  # 1, 31, 61, 91...
                # 使用B图（缩略图会采样到这些帧）
                b_frame_num = (b_frame_used % total_frames_b) + 1
                source_file = os.path.join(frames_b_dir, f'frame_{b_frame_num:06d}.png')
                source_type = 'B'
                b_frame_used += 1
                
                if i < 10:  # 只打印前10帧的信息
                    print(f"  帧{frame_number}: 使用B图(缩略图采样点) - frame_{b_frame_num:06d}.png")
            else:
                # 使用A图（正常播放主要看到这些帧）
                a_frame_num = (a_frame_used % total_frames_a) + 1
                source_file = os.path.join(frames_a_dir, f'frame_{a_frame_num:06d}.png')
                source_type = 'A'
                a_frame_used += 1
                
                if i < 10:  # 只打印前10帧的信息
                    print(f"  帧{frame_number}: 使用A图(正常播放) - frame_{a_frame_num:06d}.png")
            
            if os.path.exists(source_file):
                shutil.copy2(source_file, target_file)
            else:
                print(f"错误: 源帧文件不存在: {source_file}")
                return False
        
        print(f"序列创建完成，总帧数: {total_frames_a}")
        print(f"B图使用次数: {b_frame_used}, A图使用次数: {a_frame_used}")
        return True
        
    except Exception as e:
        print(f"缩略图欺骗序列创建失败: {e}")
        return False

def encode_thumbnail_deception_video(sequence_dir, output_path, video_a_path, fps, width, height):
    """编码缩略图欺骗视频"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False

    try:
        if not os.path.exists(sequence_dir):
            print("错误: 帧序列目录不存在")
            return False

        temp_video_path = os.path.join(os.path.dirname(sequence_dir), "temp_thumbnail_video.mp4")

        # 使用正常的编码参数，不使用任何特殊设置
        cmd = [ffmpeg_cmd, '-y',
               '-framerate', str(fps),
               '-start_number', '1',
               '-i', f'{sequence_dir}/frame_%06d.png',
               '-c:v', 'libx264',
               '-preset', 'medium',
               '-crf', '23',
               '-pix_fmt', 'yuv420p',
               '-r', str(fps),
               temp_video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"视频编码失败: {result.stderr}")
            return False

        # 合并A视频的音频
        final_cmd = [ffmpeg_cmd, '-y',
                     '-i', temp_video_path,
                     '-i', video_a_path,
                     '-c:v', 'copy',
                     '-c:a', 'copy',
                     '-map', '0:v:0',
                     '-map', '1:a:0',
                     '-shortest',
                     output_path]

        result_audio = subprocess.run(final_cmd, capture_output=True, text=True, timeout=300)
        if result_audio.returncode != 0:
            print(f"音频合并失败: {result_audio.stderr}")
            # 如果音频合并失败，直接复制视频
            shutil.copy2(temp_video_path, output_path)

        return True

    except Exception as e:
        print(f"编码失败: {e}")
        return False

def main():
    """主函数"""
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\a.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\666.mp4"
    output_path = "thumbnail_deception_result.mp4"
    
    print("开始缩略图欺骗处理...")
    print("策略: 缩略图采样点显示B图，正常播放显示A图")
    
    # 获取视频信息
    info_a = get_video_info(video_a_path)
    info_b = get_video_info(video_b_path)
    
    if not info_a or not info_b:
        print("错误: 无法获取视频信息")
        return False
    
    print(f"A视频: {info_a['width']}x{info_a['height']}, {info_a['fps']:.2f}fps, {info_a['total_frames']}帧")
    print(f"B视频: {info_b['width']}x{info_b['height']}, {info_b['fps']:.2f}fps, {info_b['total_frames']}帧")
    
    # 使用A视频的参数作为目标
    target_width = info_a['width']
    target_height = info_a['height']
    target_fps = info_a['fps']
    
    # 创建临时目录
    temp_dir = f"temp_thumbnail_{int(time.time())}"
    
    try:
        # 提取帧
        print("提取A视频帧...")
        frames_a_dir = os.path.join(temp_dir, 'frames_a')
        if not extract_frames(video_a_path, frames_a_dir, target_width, target_height):
            return False

        print("提取B视频帧...")
        frames_b_dir = os.path.join(temp_dir, 'frames_b')
        if not extract_frames(video_b_path, frames_b_dir, target_width, target_height):
            return False

        # 创建缩略图欺骗序列
        print("创建缩略图欺骗序列...")
        sequence_dir = os.path.join(temp_dir, 'sequence')
        if not create_thumbnail_deception_sequence(frames_a_dir, frames_b_dir, sequence_dir,
                                                 info_a['total_frames'], info_b['total_frames']):
            print("错误: 缩略图欺骗序列创建失败")
            return False

        # 编码视频
        print("编码视频...")
        success = encode_thumbnail_deception_video(sequence_dir, output_path, video_a_path,
                                                 target_fps, target_width, target_height)

        if success:
            abs_output_path = os.path.abspath(output_path)
            print(f"处理完成！输出文件: {abs_output_path}")
            print("测试方法:")
            print("1. 生成缩略图查看是否显示B图内容")
            print("2. 正常播放查看是否主要显示A图内容")
            return True
        else:
            print("编码失败")
            return False

    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print("临时文件已清理")
            except:
                print(f"警告: 无法清理临时目录 {temp_dir}")

if __name__ == "__main__":
    main()
