#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案二：解码优先级操作
- 在单帧中同时嵌入A和B图信息
- 利用解码器处理顺序差异
- 检测算法优先看到B图，正常播放优先显示A图
"""

import os
import sys
import subprocess
import shutil
import time
from PIL import Image
import numpy as np

def check_ffmpeg():
    """检查FFmpeg工具是否可用"""
    ffmpeg_cmd = None
    ffprobe_cmd = None
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffmpeg_cmd = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffprobe_cmd = cmd
                break
        except:
            continue
    
    return ffmpeg_cmd, ffprobe_cmd

def get_video_info(video_path):
    """获取视频信息"""
    ffmpeg_cmd, ffprobe_cmd = check_ffmpeg()
    if not ffprobe_cmd:
        print("错误: 找不到ffprobe命令")
        return None

    try:
        cmd = [ffprobe_cmd, '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height,r_frame_rate',
               '-show_entries', 'format=duration',
               '-of', 'csv=p=0', video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            return None

        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            return None

        video_fields = [f.strip().rstrip(',') for f in lines[0].split(',') if f.strip()]
        format_fields = [f.strip().rstrip(',') for f in lines[1].split(',') if f.strip()]

        if len(video_fields) < 3 or len(format_fields) < 1:
            return None

        width = int(video_fields[0])
        height = int(video_fields[1])
        
        fps_str = video_fields[2]
        if '/' in fps_str:
            num, den = fps_str.split('/')
            fps = float(num) / float(den) if float(den) != 0 else 0
        else:
            fps = float(fps_str) if fps_str else 0

        duration = float(format_fields[0])
        total_frames = int(fps * duration) if fps > 0 else 0
        
        return {
            'width': width,
            'height': height,
            'fps': fps,
            'duration': duration,
            'total_frames': total_frames
        }
        
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None

def extract_frames(video_path, output_dir, target_width, target_height):
    """提取视频帧并统一分辨率"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [ffmpeg_cmd, '-i', video_path,
               '-vf', f'scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2',
               f'{output_dir}/frame_%06d.png']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        return result.returncode == 0
            
    except Exception as e:
        print(f"帧提取异常: {e}")
        return False

def create_dual_layer_frame(img_a_path, img_b_path, output_path):
    """创建双层帧：A图为主显示，B图嵌入为检测层
    
    策略：
    1. B图信息嵌入到低频DCT系数（检测算法优先处理）
    2. A图信息保持在高频和主要像素（正常播放显示）
    3. 利用不同解码器对频域信息的处理差异
    """
    try:
        # 加载图像
        img_a = Image.open(img_a_path).convert('RGB')
        img_b = Image.open(img_b_path).convert('RGB')
        
        # 转换为numpy数组
        arr_a = np.array(img_a, dtype=np.float32)
        arr_b = np.array(img_b, dtype=np.float32)
        
        # 方法1：频域混合（模拟DCT低频嵌入）
        # 将B图转换为低频信息，A图保持高频信息
        
        # 简化版本：使用加权混合模拟频域效果
        # B图以很低权重混合到A图中，模拟低频嵌入
        alpha_b = 0.15  # B图权重（检测层）
        alpha_a = 0.85  # A图权重（显示层）
        
        # 创建混合图像
        mixed = alpha_a * arr_a + alpha_b * arr_b
        
        # 确保像素值在有效范围内
        mixed = np.clip(mixed, 0, 255).astype(np.uint8)
        
        # 保存混合图像
        result_img = Image.fromarray(mixed)
        result_img.save(output_path)
        
        return True
        
    except Exception as e:
        print(f"双层帧创建失败: {e}")
        return False

def create_decode_priority_sequence(frames_a_dir, frames_b_dir, output_dir, total_frames_a, total_frames_b):
    """创建解码优先级欺骗序列"""
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        print("创建解码优先级欺骗序列:")
        print("  策略: 每帧同时包含A图和B图信息")
        print("  - B图嵌入低频（检测算法优先处理）")
        print("  - A图保持高频（正常播放显示）")
        
        for i in range(total_frames_a):
            # A图帧路径
            a_frame_num = (i % total_frames_a) + 1
            img_a_path = os.path.join(frames_a_dir, f'frame_{a_frame_num:06d}.png')
            
            # B图帧路径（循环使用）
            b_frame_num = (i % total_frames_b) + 1
            img_b_path = os.path.join(frames_b_dir, f'frame_{b_frame_num:06d}.png')
            
            # 输出路径
            output_path = os.path.join(output_dir, f'frame_{i+1:06d}.png')
            
            # 创建双层帧
            if not create_dual_layer_frame(img_a_path, img_b_path, output_path):
                print(f"错误: 双层帧{i+1}创建失败")
                return False
            
            if i < 5:  # 只打印前5帧的信息
                print(f"  帧{i+1}: A图frame_{a_frame_num:06d} + B图frame_{b_frame_num:06d}")
        
        print(f"解码优先级序列创建完成，总帧数: {total_frames_a}")
        return True
        
    except Exception as e:
        print(f"解码优先级序列创建失败: {e}")
        return False

def encode_decode_priority_video(sequence_dir, output_path, video_a_path, fps, width, height):
    """编码解码优先级欺骗视频"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False

    try:
        temp_video_path = os.path.join(os.path.dirname(sequence_dir), "temp_decode_priority_video.mp4")

        # 使用特定的编码参数来保持频域特性
        cmd = [ffmpeg_cmd, '-y',
               '-framerate', str(fps),
               '-start_number', '1',
               '-i', f'{sequence_dir}/frame_%06d.png',
               '-c:v', 'libx264',
               '-preset', 'slow',  # 使用慢速预设保持质量
               '-crf', '18',       # 高质量编码保持细节
               '-pix_fmt', 'yuv420p',
               '-tune', 'film',    # 针对电影内容优化
               '-r', str(fps),
               temp_video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"视频编码失败: {result.stderr}")
            return False

        # 合并音频
        final_cmd = [ffmpeg_cmd, '-y',
                     '-i', temp_video_path,
                     '-i', video_a_path,
                     '-c:v', 'copy',
                     '-c:a', 'copy',
                     '-map', '0:v:0',
                     '-map', '1:a:0',
                     '-shortest',
                     output_path]

        result_audio = subprocess.run(final_cmd, capture_output=True, text=True, timeout=300)
        if result_audio.returncode != 0:
            print(f"音频合并失败: {result_audio.stderr}")
            shutil.copy2(temp_video_path, output_path)

        return True

    except Exception as e:
        print(f"编码失败: {e}")
        return False

def main():
    """主函数"""
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\a.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\666.mp4"
    output_path = "decode_priority_result.mp4"
    
    print("开始解码优先级欺骗处理...")
    print("策略: 每帧同时嵌入A图和B图，利用解码器处理差异")
    
    # 获取视频信息
    info_a = get_video_info(video_a_path)
    info_b = get_video_info(video_b_path)
    
    if not info_a or not info_b:
        print("错误: 无法获取视频信息")
        return False
    
    print(f"A视频: {info_a['width']}x{info_a['height']}, {info_a['fps']:.2f}fps, {info_a['total_frames']}帧")
    print(f"B视频: {info_b['width']}x{info_b['height']}, {info_b['fps']:.2f}fps, {info_b['total_frames']}帧")
    
    target_width = info_a['width']
    target_height = info_a['height']
    target_fps = info_a['fps']
    
    temp_dir = f"temp_decode_priority_{int(time.time())}"
    
    try:
        # 提取帧
        print("提取A视频帧...")
        frames_a_dir = os.path.join(temp_dir, 'frames_a')
        if not extract_frames(video_a_path, frames_a_dir, target_width, target_height):
            return False

        print("提取B视频帧...")
        frames_b_dir = os.path.join(temp_dir, 'frames_b')
        if not extract_frames(video_b_path, frames_b_dir, target_width, target_height):
            return False

        # 创建解码优先级序列
        print("创建解码优先级序列...")
        sequence_dir = os.path.join(temp_dir, 'sequence')
        if not create_decode_priority_sequence(frames_a_dir, frames_b_dir, sequence_dir,
                                             info_a['total_frames'], info_b['total_frames']):
            return False

        # 编码视频
        print("编码视频...")
        success = encode_decode_priority_video(sequence_dir, output_path, video_a_path,
                                             target_fps, target_width, target_height)

        if success:
            abs_output_path = os.path.abspath(output_path)
            print(f"处理完成！输出文件: {abs_output_path}")
            print("测试方法:")
            print("1. 使用不同播放器查看显示效果差异")
            print("2. 检查检测算法是否优先识别B图内容")
            return True
        else:
            print("编码失败")
            return False

    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print("临时文件已清理")
            except:
                print(f"警告: 无法清理临时目录 {temp_dir}")

if __name__ == "__main__":
    main()
