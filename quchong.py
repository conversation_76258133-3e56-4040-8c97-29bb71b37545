#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ABT - 双视频处理工具
实现bu.mp4精确帧序列模式的双视频合并
"""

import os
import sys
import subprocess
import shutil


# ============================================================================
# 工具函数
# ============================================================================

def check_ffmpeg():
    """检查FFmpeg工具是否可用，返回(ffmpeg_cmd, ffprobe_cmd)"""
    ffmpeg_cmd = None
    ffprobe_cmd = None
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffmpeg_cmd = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffprobe_cmd = cmd
                break
        except:
            continue
    
    return ffmpeg_cmd, ffprobe_cmd


def clean_field(field_str):
    """清理字段值，去除逗号、空白和其他无效字符"""
    if not field_str or field_str == 'N/A':
        return ''
    # 去除空白字符和末尾的逗号
    cleaned = field_str.strip().rstrip(',')
    return cleaned


def get_video_info(video_path):
    """获取视频信息"""
    ffmpeg_cmd, ffprobe_cmd = check_ffmpeg()
    if not ffprobe_cmd:
        print("错误: 找不到ffprobe命令")
        return None

    try:
        # 获取基本信息
        cmd = [ffprobe_cmd, '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height,r_frame_rate',
               '-show_entries', 'format=duration',
               '-of', 'csv=p=0', video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            print(f"ffprobe命令执行失败: {result.stderr}")
            return None

        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            return None

        # 解析第一行（视频流信息）
        video_fields = [clean_field(f) for f in lines[0].split(',')]
        # 过滤空字段
        video_fields = [f for f in video_fields if f]

        # 解析第二行（格式信息）
        format_fields = [clean_field(f) for f in lines[1].split(',')]
        # 过滤空字段
        format_fields = [f for f in format_fields if f]

        # 确保有足够的字段
        if len(video_fields) < 3:
            print(f"视频字段数量不足: {len(video_fields)}")
            return None

        if len(format_fields) < 1:
            print(f"格式字段数量不足: {len(format_fields)}")
            return None

        # 解析信息
        try:
            width = int(video_fields[0]) if video_fields[0] else 0
        except ValueError as e:
            print(f"宽度解析失败: '{video_fields[0]}', 错误: {e}")
            return None

        try:
            height = int(video_fields[1]) if video_fields[1] else 0
        except ValueError as e:
            print(f"高度解析失败: '{video_fields[1]}', 错误: {e}")
            return None

        # 解析帧率
        fps_str = video_fields[2] if len(video_fields) > 2 else '0/1'
        if '/' in fps_str and fps_str:
            try:
                num, den = fps_str.split('/')
                fps = float(num) / float(den) if float(den) != 0 else 0
            except (ValueError, ZeroDivisionError) as e:
                print(f"帧率解析失败: '{fps_str}', 错误: {e}")
                fps = 0
        else:
            fps = 0

        # 解析时长
        try:
            duration = float(format_fields[0]) if format_fields[0] else 0
        except ValueError as e:
            print(f"时长解析失败: '{format_fields[0]}', 错误: {e}")
            return None
        
        # 获取准确帧数
        cmd_frames = [ffprobe_cmd, '-v', 'error', '-count_frames',
                      '-select_streams', 'v:0', '-show_entries', 'stream=nb_frames',
                      '-of', 'csv=p=0', video_path]

        result_frames = subprocess.run(cmd_frames, capture_output=True, text=True, timeout=60)
        total_frames = 0
        if result_frames.returncode == 0:
            frame_count_str = clean_field(result_frames.stdout.strip())
            if frame_count_str and frame_count_str != 'N/A':
                try:
                    total_frames = int(frame_count_str)
                except ValueError as e:
                    print(f"帧数解析失败: '{frame_count_str}', 错误: {e}")
                    total_frames = 0
        
        return {
            'width': width,
            'height': height,
            'fps': fps,
            'duration': duration,
            'total_frames': total_frames
        }
        
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None


def extract_frames(video_path, output_dir):
    """提取视频帧"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        print("错误: 找不到ffmpeg命令")
        return False
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [ffmpeg_cmd, '-i', video_path,
               f'{output_dir}/frame_%06d.png']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            return True
        else:
            print(f"帧提取失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"帧提取异常: {e}")
        return False


def create_gray_frame(width, height, output_path):
    """创建灰色帧"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False
    
    try:
        cmd = [ffmpeg_cmd, '-f', 'lavfi', '-i', f'color=gray:size={width}x{height}:duration=1',
               '-frames:v', '1', '-y', output_path]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        return result.returncode == 0
        
    except:
        return False


def format_duration(seconds):
    """格式化时长"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)

    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    else:
        return f"{minutes:02d}:{secs:02d}"


def generate_bu_style_timestamps(frame_count, output_fps):
    """生成bu.mp4风格的时间戳模式"""
    timestamps = []
    current_time = 0.0

    # bu.mp4的固定模式：30fps基础间隔，不管输出帧率是多少
    base_interval = 1.0 / 30.0  # 固定30fps = 0.033333秒
    micro_interval = 0.000001   # 组内微小间隔 (1微秒)

    print(f"生成bu.mp4风格时间戳:")
    print(f"  基础间隔: {base_interval:.6f}秒 (固定30fps)")
    print(f"  组内间隔: {micro_interval:.6f}秒")
    print(f"  模式: 两帧一组，组间{base_interval:.6f}秒，组内{micro_interval:.6f}秒")

    for i in range(frame_count):
        timestamps.append(current_time)

        if i % 2 == 0:
            # 偶数帧：组的第一帧，下一帧用微小间隔
            current_time += micro_interval
        else:
            # 奇数帧：组的第二帧，下一帧用正常间隔
            current_time += base_interval - micro_interval  # 补齐到完整的0.033333

    return timestamps





# ============================================================================
# 核心算法
# ============================================================================




def arrange_frames_by_sequence(frames_a_dir, frames_b_dir, sequence_dir, frame_sequence, total_frames_a, total_frames_b):
    """根据帧序列将A帧和B帧按正确顺序重新编号排列，B视频帧循环使用"""
    try:
        os.makedirs(sequence_dir, exist_ok=True)

        a_frame_index = 1
        b_frame_index = 1



        for i, frame_type in enumerate(frame_sequence):
            target_file = os.path.join(sequence_dir, f'frame_{i+1:06d}.png')

            if frame_type == 'I' or frame_type == 'P':
                # 使用A视频的帧，循环使用
                use_a_frame = ((a_frame_index - 1) % total_frames_a) + 1
                source_file = os.path.join(frames_a_dir, f'frame_{use_a_frame:06d}.png')
                if os.path.exists(source_file):
                    shutil.copy2(source_file, target_file)
                    a_frame_index += 1
                else:
                    print(f"错误: A视频帧{use_a_frame}不存在")
                    return False

            elif frame_type == 'B':
                # 使用B视频的帧，循环使用
                use_b_frame = ((b_frame_index - 1) % total_frames_b) + 1
                source_file = os.path.join(frames_b_dir, f'frame_{use_b_frame:06d}.png')
                if os.path.exists(source_file):
                    shutil.copy2(source_file, target_file)
                    b_frame_index += 1
                else:
                    print(f"错误: B视频帧{use_b_frame}不存在")
                    return False






        return True

    except Exception as e:
        print(f"帧排列失败: {e}")
        return False


def create_frame_sequence(total_frames_a, total_frames_b, a_width, a_height):
    """生成bu.mp4确切的帧序列模式，适应视频长度"""

    # bu.mp4的确切帧序列模式（基于实际分析）：
    # - 第1帧：I帧
    # - 第2-10帧：B-B-B-P-B-B-B-P-P
    # - 第11帧开始：P-B-P-B-P-B-P-B-P-B...（PBPB交替）
    # - I帧间隔：每250帧一个I帧 (第1, 251, 501帧...)

    # 目标总帧数 = A视频帧数 × 2 (双视频合并)
    target_total_frames = total_frames_a * 2



    # 生成帧序列元组 (帧类型, 源视频, 帧索引)
    sequence = []
    a_frame_index = 1
    b_frame_index = 1

    def generate_bu_style_sequence(total_frames):
        """生成bu.mp4风格的帧序列"""
        frame_sequence = []

        for i in range(total_frames):
            # I帧位置：第1帧（索引0）和每250帧间隔（索引250, 500, 750...）
            if i == 0 or i % 250 == 0:
                frame_sequence.append('I')
            elif 1 <= i <= 9:
                # 第2-10帧：B-B-B-P-B-B-B-P-P
                pattern_2_10 = ['B', 'B', 'B', 'P', 'B', 'B', 'B', 'P', 'P']
                frame_sequence.append(pattern_2_10[i-1])
            else:
                # 第11帧开始：P-B-P-B-P-B...（PBPB交替）
                # 从第11帧开始的PBPB模式
                offset_from_11 = i - 10
                if offset_from_11 % 2 == 1:
                    frame_sequence.append('P')
                else:
                    frame_sequence.append('B')

        return frame_sequence

    # 生成完整的帧类型序列
    frame_types = generate_bu_style_sequence(target_total_frames)

    for i, frame_type in enumerate(frame_types):

        # 分配帧内容
        if frame_type == 'I' or frame_type == 'P':
            # I帧和P帧使用A视频内容
            use_a_frame = ((a_frame_index - 1) % total_frames_a) + 1
            sequence.append((frame_type, 'A', use_a_frame))
            a_frame_index += 1
        elif frame_type == 'B':
            # B帧使用B视频内容
            use_b_frame = ((b_frame_index - 1) % total_frames_b) + 1
            sequence.append((frame_type, 'B', use_b_frame))
            b_frame_index += 1





    return sequence


# ============================================================================
# 主要处理函数
# ============================================================================

def encode_video(frame_sequence, temp_dir, output_path, video_a_path, output_framerate, a_width, a_height):
    """使用x264独立CLI和qpfile强制帧类型编码视频"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False

    try:
        # 使用预排序的图片目录
        sequence_dir = os.path.join(temp_dir, 'sequence')

        # 检查预排序的图片是否存在
        if not os.path.exists(sequence_dir):
            print("错误: 预排序图片目录不存在")
            return False

        # 检查图片文件数量
        png_files = [f for f in os.listdir(sequence_dir) if f.endswith('.png')]
        if len(png_files) != len(frame_sequence):
            print(f"警告: 图片文件数量({len(png_files)})与序列长度({len(frame_sequence)})不匹配")



        # 编码视频
        temp_video_path = os.path.join(temp_dir, "temp_video.mp4")

        # 第一步：将PNG序列转换为YUV原始格式（使用计算的输出帧率）
        yuv_file = os.path.join(temp_dir, "input.yuv")
        ffmpeg_yuv_cmd = [
            ffmpeg_cmd, '-y',
            '-framerate', str(output_framerate),  # 使用计算的输出帧率
            '-start_number', '1',
            '-i', f'{sequence_dir}/frame_%06d.png',
            '-pix_fmt', 'yuv420p',
            '-f', 'rawvideo',
            yuv_file
        ]


        result_yuv = subprocess.run(ffmpeg_yuv_cmd, capture_output=True, text=True, timeout=300)
        if result_yuv.returncode != 0:
            print(f"YUV转换失败: {result_yuv.stderr}")
            return False

        # 第二步：创建x264的qpfile文件（使用我们验证成功的方案）
        qpfile = os.path.join(temp_dir, "qpfile.txt")
        with open(qpfile, 'w') as f:
            for i, (frame_type, _, _) in enumerate(frame_sequence):
                # 使用从第1帧开始编号的方案（我们验证成功的方法）
                frame_num = i + 1  # 从第1帧开始编号
                if frame_type == 'I':
                    f.write(f"{frame_num} I 18\n")  # I帧使用较低QP
                elif frame_type == 'P':
                    f.write(f"{frame_num} P 20\n")  # P帧使用中等QP
                elif frame_type == 'B':
                    f.write(f"{frame_num} b 22\n")  # 使用小写b（非参考B帧）



        # 第三步：检查x264独立CLI是否可用
        x264_cmd = None
        for cmd in ['x264', 'x264.exe']:
            try:
                result = subprocess.run([cmd, '--version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    x264_cmd = cmd
                    break
            except:
                continue

        if not x264_cmd:
            print("错误: 找不到x264独立CLI工具")
            print("请确保x264.exe在PATH中或当前目录下")
            return False

        # 第四步：使用x264独立CLI编码
        h264_file = os.path.join(temp_dir, "output.h264")

        # 使用计算的输出帧率进行x264编码
        x264_encode_cmd = [
            x264_cmd,
            '--input-res', f'{a_width}x{a_height}',
            '--fps', str(output_framerate),  # 使用计算的输出帧率
            '--qpfile', qpfile,  # 使用完美的qpfile强制帧类型
            '--preset', 'ultrafast',
            '--crf', '23',
            '--bframes', '3',
            '--b-adapt', '0',
            '--b-pyramid', 'none',
            '--no-scenecut',
            '--no-mbtree',
            '--keyint', '250',  # 250帧间隔，与我们成功测试一致
            '--min-keyint', '1',
            '--force-cfr',  # 强制恒定帧率
            '--output', h264_file,
            yuv_file
        ]



        result_x264 = subprocess.run(x264_encode_cmd, capture_output=True, text=True, timeout=300)
        if result_x264.returncode != 0:
            print(f"x264编码失败: {result_x264.stderr}")
            return False

        # 第五步：将H.264流封装为MP4（应用最终输出帧率）
        ffmpeg_mux_cmd = [
            ffmpeg_cmd, '-y',
            '-r', str(output_framerate),  # 在这里应用最终的输出帧率
            '-i', h264_file,
            '-c', 'copy',
            '-movflags', '+faststart',
            temp_video_path
        ]

        result_mux = subprocess.run(ffmpeg_mux_cmd, capture_output=True, text=True, timeout=60)
        if result_mux.returncode != 0:
            print(f"MP4封装失败: {result_mux.stderr}")
            return False

        # 第六步：合并A视频的音频
        ffmpeg_audio_cmd = [
            ffmpeg_cmd, '-y',
            '-i', temp_video_path,
            '-i', video_a_path,
            '-c:v', 'copy',
            '-c:a', 'copy',
            '-map_metadata', '0',
            '-shortest',
            output_path
        ]

        result_audio = subprocess.run(ffmpeg_audio_cmd, capture_output=True, text=True, timeout=300)
        if result_audio.returncode != 0:
            print(f"音频合并失败: {result_audio.stderr}")
            return False


        return True

    except Exception as e:
        print(f"编码失败: {e}")
        return False


def process_videos(video_a_path, video_b_path, output_path):
    """处理双视频合并"""
    # 获取视频信息
    info_a = get_video_info(video_a_path)
    info_b = get_video_info(video_b_path)

    if not info_a or not info_b:
        print("错误: 无法获取视频信息")
        return False

    # 创建临时目录
    import time
    temp_dir = f"temp_frames_{int(time.time())}"

    try:
        # 提取帧
        frames_a_dir = os.path.join(temp_dir, 'frames_a')
        if not extract_frames(video_a_path, frames_a_dir):
            return False

        frames_b_dir = os.path.join(temp_dir, 'frames_b')
        if not extract_frames(video_b_path, frames_b_dir):
            return False

        # 创建帧序列
        frame_sequence = create_frame_sequence(
            info_a['total_frames'], info_b['total_frames'],
            info_a['width'], info_a['height'])

        if not frame_sequence:
            print("错误: 无法创建帧序列")
            return False



        # 按序列排列图片帧
        sequence_dir = os.path.join(temp_dir, 'sequence')
        if not arrange_frames_by_sequence(frames_a_dir, frames_b_dir, sequence_dir,
                                        [seq[0] for seq in frame_sequence],
                                        info_a['total_frames'], info_b['total_frames']):
            print("错误: 图片帧排列失败")
            return False

        # 计算输出帧率：总帧数 ÷ A视频时长
        output_framerate = len(frame_sequence) / info_a['duration']

        # 编码视频
        success = encode_video(frame_sequence, temp_dir, output_path, video_a_path,
                             output_framerate, info_a['width'], info_a['height'])

        return success

    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
            except:
                pass


def get_video_path(video_type):
    """获取视频路径的交互函数"""
    while True:
        try:
            user_input = input(f"拖入{video_type}视频或输入路径 (按Q退出): ").strip()

            # 退出选项
            if user_input.lower() == 'q':
                return None

            # 处理拖拽文件的情况（Windows会添加引号）
            if user_input.startswith('"') and user_input.endswith('"'):
                user_input = user_input[1:-1]

            # 检查路径是否为空
            if not user_input:
                print("路径不能为空")
                continue

            # 检查文件是否存在
            if not os.path.exists(user_input):
                print("文件不存在")
                continue

            # 检查是否为视频文件
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']
            file_ext = os.path.splitext(user_input)[1].lower()
            if file_ext not in video_extensions:
                print("不支持的文件格式")
                continue

            return user_input

        except KeyboardInterrupt:
            print("\n程序已退出")
            return None
        except Exception as e:
            print("输入错误，请重新输入")
            continue


def main():
    """主程序入口"""
    print("ABT - 双视频处理工具")

    # 获取A视频路径
    video_a_path = get_video_path("A")
    if not video_a_path:
        return

    # 获取B视频路径
    video_b_path = get_video_path("B")
    if not video_b_path:
        return

    # 生成输出文件路径：A视频的文件路径 + A视频文件名 + veres
    a_dir = os.path.dirname(video_a_path)
    a_filename = os.path.splitext(os.path.basename(video_a_path))[0]
    output_path = os.path.join(a_dir, f"{a_filename}veres.mp4")

    print("正在处理...")

    # 处理视频
    success = process_videos(video_a_path, video_b_path, output_path)

    if success:
        print(f"处理完成: {output_path}")
    else:
        print("处理失败")


if __name__ == "__main__":
    main()
