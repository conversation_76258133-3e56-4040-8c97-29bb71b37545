#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ABT双视频处理工具 - 每帧双图嵌入技术
主要功能：
1. 在每一帧中嵌入完整的A图和B图数据
2. A图作为正常显示内容（用户观看）
3. B图嵌入PNG的tEXt块中（平台检测）
4. 利用播放器和检测系统对数据处理的差异

技术特点：
- 每一帧都包含完整的A图和B图数据
- A图：PNG图像数据（播放器正常显示）
- B图：嵌入PNG的tEXt块中（检测系统可发现）
- 编码层面实现，不改变透明度
- 解码时播放器跳过B图，检测系统发现B图
"""

import os
import subprocess
import shutil
import time
import base64
from PIL import Image
import io


def check_ffmpeg():
    """检查FFmpeg工具是否可用"""
    ffmpeg_cmd = None
    ffprobe_cmd = None
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffmpeg_cmd = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffprobe_cmd = cmd
                break
        except:
            continue
    
    return ffmpeg_cmd, ffprobe_cmd


def get_video_info(video_path):
    """获取视频信息"""
    ffmpeg_cmd, ffprobe_cmd = check_ffmpeg()
    if not ffprobe_cmd:
        return None

    try:
        cmd = [ffprobe_cmd, '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height,r_frame_rate',
               '-show_entries', 'format=duration',
               '-of', 'csv=p=0', video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            return None

        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            return None

        video_fields = [f.strip().rstrip(',') for f in lines[0].split(',') if f.strip()]
        format_fields = [f.strip().rstrip(',') for f in lines[1].split(',') if f.strip()]

        if len(video_fields) < 3 or len(format_fields) < 1:
            return None

        width = int(video_fields[0])
        height = int(video_fields[1])
        
        fps_str = video_fields[2]
        if '/' in fps_str:
            num, den = fps_str.split('/')
            fps = float(num) / float(den) if float(den) != 0 else 0
        else:
            fps = float(fps_str) if fps_str else 0

        duration = float(format_fields[0])
        total_frames = int(fps * duration) if fps > 0 else 0
        
        return {
            'width': width,
            'height': height,
            'fps': fps,
            'duration': duration,
            'total_frames': total_frames
        }
        
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None


def extract_frames(video_path, output_dir, target_width, target_height):
    """提取视频帧并统一分辨率"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [ffmpeg_cmd, '-i', video_path,
               '-vf', f'scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2',
               f'{output_dir}/frame_%06d.png']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        return result.returncode == 0
            
    except Exception as e:
        print(f"帧提取异常: {e}")
        return False


def create_dual_image_frame(a_frame_path, b_frame_path, output_path):
    """创建包含A图和B图的单帧"""
    try:
        # 加载A图
        a_image = Image.open(a_frame_path).convert('RGB')
        
        # 加载B图并压缩
        b_image = Image.open(b_frame_path).convert('RGB')
        
        # 将B图压缩为JPEG格式
        jpeg_buffer = io.BytesIO()
        b_image.save(jpeg_buffer, format='JPEG', quality=80, optimize=True)
        b_jpeg_data = jpeg_buffer.getvalue()
        
        # 将B图数据编码为base64
        b_base64 = base64.b64encode(b_jpeg_data).decode('ascii')
        
        # 创建PNG信息字典，嵌入B图数据
        try:
            from PIL.PngImagePlugin import PngInfo
            pnginfo = PngInfo()
            pnginfo.add_text("BIMG_DATA", b_base64)  # B图数据
            pnginfo.add_text("BIMG_SIZE", str(len(b_jpeg_data)))  # B图大小
            pnginfo.add_text("BIMG_FORMAT", "JPEG")  # B图格式
            pnginfo.add_text("DUAL_IMAGE", "TRUE")  # 双图标识
            
            # 保存A图，同时嵌入B图数据到PNG的tEXt块中
            a_image.save(output_path, format='PNG', pnginfo=pnginfo)
        except ImportError:
            # 如果PngInfo不可用，使用简单的文件名嵌入方法
            a_image.save(output_path, format='PNG')
            
            # 将B图数据保存为同名的.txt文件
            txt_path = output_path.replace('.png', '_bimg.txt')
            with open(txt_path, 'w') as f:
                f.write(f"BIMG_DATA={b_base64}\n")
                f.write(f"BIMG_SIZE={len(b_jpeg_data)}\n")
                f.write(f"BIMG_FORMAT=JPEG\n")
                f.write(f"DUAL_IMAGE=TRUE\n")
        
        return True
        
    except Exception as e:
        print(f"双图帧创建失败: {e}")
        return False


def create_per_frame_dual_sequence(frames_a_dir, frames_b_dir, output_dir, total_frames_a, total_frames_b):
    """创建每帧双图序列"""
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        print("创建每帧双图序列:")
        print("  策略: 每一帧都包含完整的A图和B图数据")
        print("  - A图: PNG图像数据（正常显示）")
        print("  - B图: 嵌入PNG的tEXt块中（隐藏数据）")
        
        # 检查实际提取的帧数
        a_frames = [f for f in os.listdir(frames_a_dir) if f.endswith('.png')]
        actual_frames_a = len(a_frames)
        
        print(f"  实际A帧数: {actual_frames_a}, 预期A帧数: {total_frames_a}")
        
        # 使用实际帧数
        process_frames = min(actual_frames_a, total_frames_a)
        
        for i in range(process_frames):
            # A图帧路径
            a_frame_num = i + 1
            a_frame_path = os.path.join(frames_a_dir, f'frame_{a_frame_num:06d}.png')
            
            # B图帧路径（循环使用）
            b_frame_num = (i % total_frames_b) + 1
            b_frame_path = os.path.join(frames_b_dir, f'frame_{b_frame_num:06d}.png')
            
            # 输出路径
            output_path = os.path.join(output_dir, f'frame_{i+1:06d}.png')
            
            # 创建双图帧
            if not create_dual_image_frame(a_frame_path, b_frame_path, output_path):
                print(f"错误: 双图帧{i+1}创建失败")
                return False
            
            if i < 5:  # 只打印前5帧的信息
                print(f"  帧{i+1}: A图frame_{a_frame_num:06d} + B图frame_{b_frame_num:06d} (嵌入tEXt)")
        
        print(f"每帧双图序列创建完成，总帧数: {process_frames}")
        print("每一帧都包含完整的A图和B图数据")
        return process_frames
        
    except Exception as e:
        print(f"每帧双图序列创建失败: {e}")
        return False


def encode_per_frame_dual_video(sequence_dir, output_path, video_a_path, fps, width, height):
    """编码每帧双图视频"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False

    try:
        print("编码每帧双图视频...")
        
        temp_video_path = os.path.join(os.path.dirname(sequence_dir), "temp_dual_video.mp4")
        
        # 编码视频（PNG序列包含嵌入的B图数据）
        cmd = [ffmpeg_cmd, '-y',
               '-framerate', str(fps),
               '-start_number', '1',
               '-i', f'{sequence_dir}/frame_%06d.png',
               '-c:v', 'libx264',
               '-preset', 'medium',
               '-crf', '23',
               '-pix_fmt', 'yuv420p',
               '-r', str(fps),
               temp_video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"视频编码失败: {result.stderr}")
            return False

        print("视频编码成功")
        
        # 合并音频
        print("合并音频...")
        final_cmd = [ffmpeg_cmd, '-y',
                     '-i', temp_video_path,
                     '-i', video_a_path,
                     '-c:v', 'copy',
                     '-c:a', 'copy',
                     '-map', '0:v:0',
                     '-map', '1:a:0',
                     '-shortest',
                     output_path]

        result_audio = subprocess.run(final_cmd, capture_output=True, text=True, timeout=300)
        if result_audio.returncode != 0:
            print(f"音频合并失败: {result_audio.stderr}")
            shutil.copy2(temp_video_path, output_path)

        return True

    except Exception as e:
        print(f"编码失败: {e}")
        return False


def process_videos(video_a_path, video_b_path, output_path):
    """处理双视频的主函数"""
    print("开始每帧双图嵌入处理...")
    print("技术原理:")
    print("  - 每一帧都包含完整的A图和B图数据")
    print("  - A图: PNG图像数据（播放器显示）")
    print("  - B图: 嵌入PNG的tEXt块中（检测系统分析）")
    
    # 获取视频信息
    info_a = get_video_info(video_a_path)
    info_b = get_video_info(video_b_path)
    
    if not info_a or not info_b:
        print("错误: 无法获取视频信息")
        return False
    
    print(f"A视频: {info_a['width']}x{info_a['height']}, {info_a['fps']:.2f}fps, {info_a['total_frames']}帧")
    print(f"B视频: {info_b['width']}x{info_b['height']}, {info_b['fps']:.2f}fps, {info_b['total_frames']}帧")
    
    target_width = info_a['width']
    target_height = info_a['height']
    target_fps = info_a['fps']
    
    temp_dir = f"temp_per_frame_{int(time.time())}"
    
    try:
        # 提取帧
        print("提取A视频帧...")
        frames_a_dir = os.path.join(temp_dir, 'frames_a')
        if not extract_frames(video_a_path, frames_a_dir, target_width, target_height):
            return False

        print("提取B视频帧...")
        frames_b_dir = os.path.join(temp_dir, 'frames_b')
        if not extract_frames(video_b_path, frames_b_dir, target_width, target_height):
            return False

        # 创建每帧双图序列
        print("创建每帧双图序列...")
        sequence_dir = os.path.join(temp_dir, 'sequence')
        actual_frames = create_per_frame_dual_sequence(frames_a_dir, frames_b_dir, sequence_dir,
                                                     info_a['total_frames'], info_b['total_frames'])
        if not actual_frames:
            return False

        # 编码视频
        print("编码每帧双图视频...")
        success = encode_per_frame_dual_video(sequence_dir, output_path, video_a_path,
                                            target_fps, target_width, target_height)

        if success:
            print(f"✓ 确认: 每一帧({actual_frames}帧)都包含完整的A图和B图数据")
            return True
        else:
            print("编码失败")
            return False

    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print("临时文件已清理")
            except:
                print(f"警告: 无法清理临时目录 {temp_dir}")


def get_video_path(video_type):
    """获取视频路径的交互函数"""
    while True:
        try:
            user_input = input(f"拖入{video_type}视频或输入路径 (按Q退出): ").strip()

            # 退出选项
            if user_input.lower() == 'q':
                return None

            # 处理拖拽文件的情况（Windows会添加引号）
            if user_input.startswith('"') and user_input.endswith('"'):
                user_input = user_input[1:-1]

            # 检查路径是否为空
            if not user_input:
                print("路径不能为空")
                continue

            # 检查文件是否存在
            if not os.path.exists(user_input):
                print("文件不存在")
                continue

            # 检查是否为视频文件
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']
            file_ext = os.path.splitext(user_input)[1].lower()
            if file_ext not in video_extensions:
                print("不支持的文件格式")
                continue

            return user_input

        except KeyboardInterrupt:
            print("\n程序已退出")
            return None
        except Exception as e:
            print("输入错误，请重新输入")
            continue


def main():
    """主程序入口"""
    print("ABT - 双视频处理工具")

    # 获取A视频路径
    video_a_path = get_video_path("A")
    if not video_a_path:
        return

    # 获取B视频路径
    video_b_path = get_video_path("B")
    if not video_b_path:
        return

    # 生成输出文件路径：A视频的文件路径 + A视频文件名 + veres
    a_dir = os.path.dirname(video_a_path)
    a_filename = os.path.splitext(os.path.basename(video_a_path))[0]
    output_path = os.path.join(a_dir, f"{a_filename}veres.mp4")

    print("正在处理...")

    # 处理视频
    success = process_videos(video_a_path, video_b_path, output_path)

    if success:
        print(f"处理完成: {output_path}")
    else:
        print("处理失败")


if __name__ == "__main__":
    main()
