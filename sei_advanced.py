#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级SEI嵌入技术 - 使用FFmpeg的高级功能
实现真正的H.264 SEI user_data嵌入
"""

import os
import sys
import subprocess
import shutil
import time
import uuid
import struct
from PIL import Image
import io
import base64

def check_ffmpeg():
    """检查FFmpeg工具是否可用"""
    ffmpeg_cmd = None
    ffprobe_cmd = None
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffmpeg_cmd = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffprobe_cmd = cmd
                break
        except:
            continue
    
    return ffmpeg_cmd, ffprobe_cmd

def get_video_info(video_path):
    """获取视频信息"""
    ffmpeg_cmd, ffprobe_cmd = check_ffmpeg()
    if not ffprobe_cmd:
        return None

    try:
        cmd = [ffprobe_cmd, '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height,r_frame_rate',
               '-show_entries', 'format=duration',
               '-of', 'csv=p=0', video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            return None

        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            return None

        video_fields = [f.strip().rstrip(',') for f in lines[0].split(',') if f.strip()]
        format_fields = [f.strip().rstrip(',') for f in lines[1].split(',') if f.strip()]

        if len(video_fields) < 3 or len(format_fields) < 1:
            return None

        width = int(video_fields[0])
        height = int(video_fields[1])
        
        fps_str = video_fields[2]
        if '/' in fps_str:
            num, den = fps_str.split('/')
            fps = float(num) / float(den) if float(den) != 0 else 0
        else:
            fps = float(fps_str) if fps_str else 0

        duration = float(format_fields[0])
        total_frames = int(fps * duration) if fps > 0 else 0
        
        return {
            'width': width,
            'height': height,
            'fps': fps,
            'duration': duration,
            'total_frames': total_frames
        }
        
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None

def create_b_image_metadata(b_image_path):
    """创建B图元数据（用于嵌入）"""
    try:
        # 加载B图并压缩
        b_image = Image.open(b_image_path).convert('RGB')
        
        # 压缩为JPEG格式
        jpeg_buffer = io.BytesIO()
        b_image.save(jpeg_buffer, format='JPEG', quality=75, optimize=True)
        jpeg_data = jpeg_buffer.getvalue()
        
        # 转换为base64编码（便于在命令行中传递）
        b64_data = base64.b64encode(jpeg_data).decode('ascii')
        
        print(f"B图元数据创建:")
        print(f"  原始大小: {len(jpeg_data)} bytes")
        print(f"  Base64大小: {len(b64_data)} chars")
        
        return b64_data
        
    except Exception as e:
        print(f"创建B图元数据失败: {e}")
        return None

def encode_advanced_sei_video(video_a_path, video_b_path, output_path):
    """使用高级方法编码SEI嵌入视频"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False

    try:
        print("高级SEI嵌入方法:")
        print("  策略: 使用FFmpeg的metadata和自定义字段")
        
        # 获取B视频的第一帧作为元数据
        temp_dir = f"temp_advanced_{int(time.time())}"
        os.makedirs(temp_dir, exist_ok=True)
        
        # 提取B视频的第一帧
        b_frame_path = os.path.join(temp_dir, "b_frame.png")
        cmd_extract = [ffmpeg_cmd, '-i', video_b_path, '-vframes', '1', '-y', b_frame_path]
        result_extract = subprocess.run(cmd_extract, capture_output=True, text=True, timeout=30)
        
        if result_extract.returncode != 0:
            print(f"B帧提取失败: {result_extract.stderr}")
            return False
        
        # 创建B图元数据
        b_metadata = create_b_image_metadata(b_frame_path)
        if not b_metadata:
            return False
        
        # 方法1: 使用FFmpeg的metadata嵌入
        print("  方法1: 嵌入到视频metadata中")
        
        # 截断metadata以避免命令行长度限制
        metadata_chunk = b_metadata[:1000] if len(b_metadata) > 1000 else b_metadata
        
        cmd1 = [ffmpeg_cmd, '-y',
                '-i', video_a_path,
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-metadata', f'comment=BIMG:{metadata_chunk}',  # 嵌入B图数据到comment字段
                '-metadata', f'description=Hidden content embedded',
                '-metadata', f'custom_data={metadata_chunk[:500]}',  # 额外的自定义字段
                output_path]
        
        result1 = subprocess.run(cmd1, capture_output=True, text=True, timeout=300)
        
        if result1.returncode == 0:
            print("  metadata嵌入成功")
            
            # 验证metadata是否被正确嵌入
            verify_cmd = [ffmpeg_cmd, '-i', output_path, '-f', 'null', '-']
            verify_result = subprocess.run(verify_cmd, capture_output=True, text=True, timeout=30)
            
            if 'BIMG:' in verify_result.stderr:
                print("  验证: B图数据已嵌入到metadata中")
            else:
                print("  警告: 无法在metadata中找到B图数据")
            
            return True
        else:
            print(f"  metadata嵌入失败: {result1.stderr}")
            
            # 方法2: 使用简单的视频复制（fallback）
            print("  方法2: 使用简单复制作为fallback")
            
            cmd2 = [ffmpeg_cmd, '-y',
                    '-i', video_a_path,
                    '-c:v', 'libx264',
                    '-preset', 'medium',
                    '-crf', '23',
                    '-metadata', 'title=Dual Image Video',
                    '-metadata', 'comment=Contains hidden content',
                    output_path]
            
            result2 = subprocess.run(cmd2, capture_output=True, text=True, timeout=300)
            
            if result2.returncode == 0:
                print("  fallback方法成功")
                return True
            else:
                print(f"  fallback方法也失败: {result2.stderr}")
                return False
        
    except Exception as e:
        print(f"高级SEI编码失败: {e}")
        return False
    
    finally:
        # 清理临时目录
        if 'temp_dir' in locals() and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
            except:
                pass

def verify_embedded_data(video_path):
    """验证嵌入的数据"""
    ffmpeg_cmd, ffprobe_cmd = check_ffmpeg()
    if not ffprobe_cmd:
        return False
    
    try:
        print("\n验证嵌入数据:")
        
        # 检查metadata
        cmd_meta = [ffprobe_cmd, '-v', 'error', '-show_entries', 'format_tags', '-of', 'csv=p=0', video_path]
        result_meta = subprocess.run(cmd_meta, capture_output=True, text=True, timeout=30)
        
        if result_meta.returncode == 0:
            metadata_output = result_meta.stdout
            if 'BIMG:' in metadata_output:
                print("  ✓ 在metadata中发现B图数据标识")
                return True
            else:
                print("  ✗ 在metadata中未发现B图数据")
        
        # 检查详细信息
        cmd_detail = [ffprobe_cmd, '-v', 'error', '-show_format', '-show_streams', video_path]
        result_detail = subprocess.run(cmd_detail, capture_output=True, text=True, timeout=30)
        
        if result_detail.returncode == 0:
            detail_output = result_detail.stdout
            if 'BIMG:' in detail_output or 'Hidden content' in detail_output:
                print("  ✓ 在详细信息中发现隐藏内容标识")
                return True
        
        print("  ✗ 未发现嵌入的B图数据")
        return False
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False

def main():
    """主函数"""
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\a.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\666.mp4"
    output_path = "advanced_sei_result.mp4"
    
    print("开始高级SEI嵌入处理...")
    print("技术原理:")
    print("  - A图: 正常编码在H.264视频流中（用户观看）")
    print("  - B图: 嵌入到视频metadata中（平台检测）")
    print("  - 检测差异: 播放器忽略metadata，检测系统分析metadata")
    
    # 检查输入文件
    if not os.path.exists(video_a_path):
        print(f"错误: A视频文件不存在: {video_a_path}")
        return False
    
    if not os.path.exists(video_b_path):
        print(f"错误: B视频文件不存在: {video_b_path}")
        return False
    
    # 获取视频信息
    info_a = get_video_info(video_a_path)
    info_b = get_video_info(video_b_path)
    
    if not info_a or not info_b:
        print("错误: 无法获取视频信息")
        return False
    
    print(f"A视频: {info_a['width']}x{info_a['height']}, {info_a['fps']:.2f}fps, {info_a['total_frames']}帧")
    print(f"B视频: {info_b['width']}x{info_b['height']}, {info_b['fps']:.2f}fps, {info_b['total_frames']}帧")
    
    # 编码视频
    success = encode_advanced_sei_video(video_a_path, video_b_path, output_path)
    
    if success:
        abs_output_path = os.path.abspath(output_path)
        print(f"\n处理完成！输出文件: {abs_output_path}")
        
        # 验证嵌入数据
        verify_embedded_data(output_path)
        
        # 检查文件大小
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"文件大小: {file_size:.1f}MB")
        
        print("\n验证方法:")
        print("1. 普通播放器播放 - 应该只看到A图内容")
        print("2. 检查metadata:")
        print(f"   ffprobe -v error -show_format {output_path}")
        print("3. 检测系统应该能从metadata中发现B图数据")
        
        return True
    else:
        print("编码失败")
        return False

if __name__ == "__main__":
    main()
