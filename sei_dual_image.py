#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
H.264 SEI User Data 嵌入技术
在单帧中嵌入AB两张完整图片：
- A图：正常编码在H.264视频流中（用户观看）
- B图：完整嵌入在SEI user_data_unregistered消息中（平台检测）
"""

import os
import sys
import subprocess
import shutil
import time
import uuid
import struct
from PIL import Image
import io

def check_ffmpeg():
    """检查FFmpeg工具是否可用"""
    ffmpeg_cmd = None
    ffprobe_cmd = None
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffmpeg_cmd = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffprobe_cmd = cmd
                break
        except:
            continue
    
    return ffmpeg_cmd, ffprobe_cmd

def get_video_info(video_path):
    """获取视频信息"""
    ffmpeg_cmd, ffprobe_cmd = check_ffmpeg()
    if not ffprobe_cmd:
        return None

    try:
        cmd = [ffprobe_cmd, '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height,r_frame_rate',
               '-show_entries', 'format=duration',
               '-of', 'csv=p=0', video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            return None

        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            return None

        video_fields = [f.strip().rstrip(',') for f in lines[0].split(',') if f.strip()]
        format_fields = [f.strip().rstrip(',') for f in lines[1].split(',') if f.strip()]

        if len(video_fields) < 3 or len(format_fields) < 1:
            return None

        width = int(video_fields[0])
        height = int(video_fields[1])
        
        fps_str = video_fields[2]
        if '/' in fps_str:
            num, den = fps_str.split('/')
            fps = float(num) / float(den) if float(den) != 0 else 0
        else:
            fps = float(fps_str) if fps_str else 0

        duration = float(format_fields[0])
        total_frames = int(fps * duration) if fps > 0 else 0
        
        return {
            'width': width,
            'height': height,
            'fps': fps,
            'duration': duration,
            'total_frames': total_frames
        }
        
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None

def extract_frames(video_path, output_dir, target_width, target_height):
    """提取视频帧并统一分辨率"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [ffmpeg_cmd, '-i', video_path,
               '-vf', f'scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2',
               f'{output_dir}/frame_%06d.png']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        return result.returncode == 0
            
    except Exception as e:
        print(f"帧提取异常: {e}")
        return False

def create_sei_user_data(b_image_path, uuid_bytes=None):
    """创建SEI user_data_unregistered消息
    
    SEI消息结构：
    - UUID (16 bytes): 自定义标识符
    - Magic Number (4 bytes): 0x42494D47 ("BIMG")
    - Image Format (1 byte): 0x01 (JPEG)
    - Image Size (4 bytes): B图数据大小
    - Image Data (variable): 压缩的B图数据
    """
    try:
        # 生成或使用指定的UUID
        if uuid_bytes is None:
            # 使用固定UUID确保一致性
            uuid_bytes = uuid.UUID('12345678-1234-5678-9abc-123456789abc').bytes
        
        # 加载B图并压缩为JPEG
        b_image = Image.open(b_image_path).convert('RGB')
        
        # 压缩为JPEG格式（高压缩率）
        jpeg_buffer = io.BytesIO()
        b_image.save(jpeg_buffer, format='JPEG', quality=85, optimize=True)
        jpeg_data = jpeg_buffer.getvalue()
        
        # 构建SEI user_data payload
        payload = bytearray()
        
        # UUID (16 bytes)
        payload.extend(uuid_bytes)
        
        # Magic Number (4 bytes): "BIMG"
        payload.extend(b'BIMG')
        
        # Image Format (1 byte): JPEG = 0x01
        payload.append(0x01)
        
        # Image Size (4 bytes, big-endian)
        payload.extend(struct.pack('>I', len(jpeg_data)))
        
        # Image Data
        payload.extend(jpeg_data)
        
        print(f"创建SEI user_data消息:")
        print(f"  UUID: {uuid.UUID(bytes=uuid_bytes)}")
        print(f"  B图格式: JPEG")
        print(f"  B图大小: {len(jpeg_data)} bytes")
        print(f"  SEI总大小: {len(payload)} bytes")
        
        return bytes(payload)
        
    except Exception as e:
        print(f"创建SEI user_data失败: {e}")
        return None

def create_sei_dual_image_sequence(frames_a_dir, frames_b_dir, output_dir, total_frames_a, total_frames_b):
    """创建SEI双图嵌入序列"""
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        print("创建SEI双图嵌入序列:")
        print("  策略: A图正常编码，B图嵌入SEI user_data")
        
        # 生成固定UUID用于所有帧
        sei_uuid = uuid.UUID('12345678-1234-5678-9abc-123456789abc').bytes
        
        for i in range(total_frames_a):
            # A图帧路径（正常复制）
            a_frame_num = (i % total_frames_a) + 1
            a_frame_path = os.path.join(frames_a_dir, f'frame_{a_frame_num:06d}.png')
            output_path = os.path.join(output_dir, f'frame_{i+1:06d}.png')
            
            # 复制A图作为主要显示内容
            if os.path.exists(a_frame_path):
                shutil.copy2(a_frame_path, output_path)
            else:
                print(f"错误: A图帧{a_frame_num}不存在")
                return False
            
            # 为每帧创建对应的SEI数据文件
            b_frame_num = (i % total_frames_b) + 1
            b_frame_path = os.path.join(frames_b_dir, f'frame_{b_frame_num:06d}.png')
            
            if os.path.exists(b_frame_path):
                # 创建SEI user_data
                sei_data = create_sei_user_data(b_frame_path, sei_uuid)
                if sei_data:
                    # 保存SEI数据文件
                    sei_file_path = os.path.join(output_dir, f'sei_{i+1:06d}.bin')
                    with open(sei_file_path, 'wb') as f:
                        f.write(sei_data)
                    
                    if i < 5:  # 只打印前5帧的信息
                        print(f"  帧{i+1}: A图frame_{a_frame_num:06d} + SEI嵌入B图frame_{b_frame_num:06d}")
                else:
                    print(f"错误: 帧{i+1}的SEI数据创建失败")
                    return False
            else:
                print(f"错误: B图帧{b_frame_num}不存在")
                return False
        
        print(f"SEI双图序列创建完成，总帧数: {total_frames_a}")
        return True
        
    except Exception as e:
        print(f"SEI双图序列创建失败: {e}")
        return False

def encode_sei_dual_image_video(sequence_dir, output_path, video_a_path, fps, width, height, total_frames):
    """编码SEI双图嵌入视频"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False

    try:
        temp_video_path = os.path.join(os.path.dirname(sequence_dir), "temp_sei_video.mp4")
        
        print("编码SEI双图视频...")
        print("  第一步: 编码A图视频流")
        
        # 第一步：正常编码A图序列
        cmd1 = [ffmpeg_cmd, '-y',
                '-framerate', str(fps),
                '-start_number', '1',
                '-i', f'{sequence_dir}/frame_%06d.png',
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-pix_fmt', 'yuv420p',
                '-r', str(fps),
                temp_video_path]

        result1 = subprocess.run(cmd1, capture_output=True, text=True, timeout=300)
        if result1.returncode != 0:
            print(f"A图视频编码失败: {result1.stderr}")
            return False
        
        print("  第二步: 使用x264直接嵌入SEI user_data")

        # 第二步：使用x264的SEI插入功能重新编码
        sei_files = [f for f in os.listdir(sequence_dir) if f.startswith('sei_') and f.endswith('.bin')]

        if not sei_files:
            print("警告: 没有找到SEI数据文件，使用原始视频")
            final_video = temp_video_path
        else:
            # 使用第一个SEI文件创建SEI参数
            first_sei_file = os.path.join(sequence_dir, 'sei_000001.bin')

            if os.path.exists(first_sei_file):
                # 读取SEI数据
                with open(first_sei_file, 'rb') as f:
                    sei_data = f.read()

                # 创建SEI文本文件（x264格式）
                sei_text_file = os.path.join(os.path.dirname(sequence_dir), "sei_data.txt")

                # 将SEI数据转换为x264可识别的格式
                # 格式：frame_number sei_type payload_hex
                with open(sei_text_file, 'w') as f:
                    # 为每个I帧（假设每30帧一个I帧）添加SEI
                    for frame_num in range(1, total_frames + 1, 30):
                        # SEI type 5 = user_data_unregistered
                        sei_hex = sei_data.hex()
                        f.write(f"{frame_num} 5 {sei_hex}\n")

                print(f"创建SEI文本文件: {sei_text_file}")
                print(f"SEI数据大小: {len(sei_data)} bytes")

                # 使用x264重新编码并嵌入SEI
                sei_video_path = os.path.join(os.path.dirname(sequence_dir), "sei_embedded_video.mp4")

                # 检查x264工具
                x264_cmd = None
                for cmd in ['x264', 'x264.exe', './x264_stable.exe']:
                    try:
                        result = subprocess.run([cmd, '--version'], capture_output=True, timeout=5)
                        if result.returncode == 0:
                            x264_cmd = cmd
                            break
                    except:
                        continue

                if x264_cmd:
                    print("使用x264嵌入SEI数据...")

                    # 先将视频转换为YUV
                    yuv_file = os.path.join(os.path.dirname(sequence_dir), "temp.yuv")
                    cmd_yuv = [ffmpeg_cmd, '-y', '-i', temp_video_path, '-pix_fmt', 'yuv420p', '-f', 'rawvideo', yuv_file]
                    result_yuv = subprocess.run(cmd_yuv, capture_output=True, text=True, timeout=300)

                    if result_yuv.returncode == 0:
                        # 使用x264编码并嵌入SEI
                        cmd_x264 = [x264_cmd,
                                   '--input-res', f'{width}x{height}',
                                   '--fps', str(fps),
                                   '--preset', 'medium',
                                   '--crf', '23',
                                   '--sei', sei_text_file,  # 嵌入SEI数据
                                   '--output', sei_video_path,
                                   yuv_file]

                        result_x264 = subprocess.run(cmd_x264, capture_output=True, text=True, timeout=300)

                        if result_x264.returncode == 0:
                            print("x264 SEI嵌入成功")
                            final_video = sei_video_path
                        else:
                            print(f"x264 SEI嵌入失败: {result_x264.stderr}")
                            final_video = temp_video_path
                    else:
                        print("YUV转换失败，使用原始视频")
                        final_video = temp_video_path
                else:
                    print("未找到x264工具，使用原始视频")
                    final_video = temp_video_path
            else:
                print("警告: SEI数据文件不存在，使用原始视频")
                final_video = temp_video_path

        # 第三步：合并音频
        print("  第三步: 合并音频")
        final_cmd = [ffmpeg_cmd, '-y',
                     '-i', final_video,
                     '-i', video_a_path,
                     '-c:v', 'copy',
                     '-c:a', 'copy',
                     '-map', '0:v:0',
                     '-map', '1:a:0',
                     '-shortest',
                     output_path]

        result_final = subprocess.run(final_cmd, capture_output=True, text=True, timeout=300)
        if result_final.returncode != 0:
            print(f"音频合并失败: {result_final.stderr}")
            shutil.copy2(final_video, output_path)

        return True

    except Exception as e:
        print(f"编码失败: {e}")
        return False

def main():
    """主函数"""
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\a.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\666.mp4"
    output_path = "sei_dual_image_result.mp4"
    
    print("开始H.264 SEI双图嵌入处理...")
    print("技术原理:")
    print("  - A图: 正常编码在H.264视频流中（用户观看）")
    print("  - B图: 完整嵌入在SEI user_data消息中（平台检测）")
    print("  - 解码差异: 播放器忽略SEI，检测系统解析SEI")
    
    # 获取视频信息
    info_a = get_video_info(video_a_path)
    info_b = get_video_info(video_b_path)
    
    if not info_a or not info_b:
        print("错误: 无法获取视频信息")
        return False
    
    print(f"A视频: {info_a['width']}x{info_a['height']}, {info_a['fps']:.2f}fps, {info_a['total_frames']}帧")
    print(f"B视频: {info_b['width']}x{info_b['height']}, {info_b['fps']:.2f}fps, {info_b['total_frames']}帧")
    
    target_width = info_a['width']
    target_height = info_a['height']
    target_fps = info_a['fps']
    
    temp_dir = f"temp_sei_{int(time.time())}"
    
    try:
        # 提取帧
        print("提取A视频帧...")
        frames_a_dir = os.path.join(temp_dir, 'frames_a')
        if not extract_frames(video_a_path, frames_a_dir, target_width, target_height):
            return False

        print("提取B视频帧...")
        frames_b_dir = os.path.join(temp_dir, 'frames_b')
        if not extract_frames(video_b_path, frames_b_dir, target_width, target_height):
            return False

        # 创建SEI双图序列
        print("创建SEI双图序列...")
        sequence_dir = os.path.join(temp_dir, 'sequence')
        if not create_sei_dual_image_sequence(frames_a_dir, frames_b_dir, sequence_dir,
                                            info_a['total_frames'], info_b['total_frames']):
            return False

        # 编码视频
        print("编码SEI双图视频...")
        success = encode_sei_dual_image_video(sequence_dir, output_path, video_a_path,
                                            target_fps, target_width, target_height, info_a['total_frames'])

        if success:
            abs_output_path = os.path.abspath(output_path)
            print(f"处理完成！输出文件: {abs_output_path}")
            print("\n验证方法:")
            print("1. 普通播放器播放 - 应该只看到A图内容")
            print("2. 使用ffprobe分析SEI消息:")
            print(f"   ffprobe -v error -show_entries packet=data -select_streams v {output_path}")
            print("3. 检测系统应该能从SEI中提取B图数据")
            return True
        else:
            print("编码失败")
            return False

    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print("临时文件已清理")
            except:
                print(f"警告: 无法清理临时目录 {temp_dir}")

if __name__ == "__main__":
    main()
